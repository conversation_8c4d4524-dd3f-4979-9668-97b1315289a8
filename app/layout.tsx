import './globals.css'
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import { ThemeProvider } from "@/providers/ThemeProvider";
// Temporarily remove potentially problematic imports
// import { AccessibilityProvider } from '@/providers/AccessibilityProvider'
// import { AccessibilityMenu } from '@/components/ui/AccessibilityMenu'
// import { CookieConsent } from '@/components/ui/CookieConsent'
import { Analytics } from "@vercel/analytics/react";
import { SpeedInsights } from "@vercel/speed-insights/next";
// import GoogleAnalytics from "@/components/GoogleAnalytics";
// import WebVitalsOptimizer from "@/components/performance/WebVitalsOptimizer";
import ClientOnly from "@/components/ui/ClientOnly";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Innovatio Pro - Digital Solutions & Web Development",
  description:
    "Professional web development, mobile apps, and digital solutions for modern businesses. Expert team delivering cutting-edge technology solutions.",
  keywords:
    "web development, mobile apps, digital solutions, React, Next.js, Flutter, AI integration",
  authors: [{ name: "Innovatio Pro Team" }],
  creator: "Innovatio Pro",
  publisher: "Innovatio Pro",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://innovatio-pro.com"),
  alternates: {
    canonical: "/",
    languages: {
      en: "/en",
      de: "/de",
      ar: "/ar",
      tr: "/tr",
      ru: "/ru",
    },
  },
  openGraph: {
    title: "Innovatio Pro - Digital Solutions & Web Development",
    description:
      "Professional web development, mobile apps, and digital solutions for modern businesses.",
    url: "https://innovatio-pro.com",
    siteName: "Innovatio Pro",
    images: [
      {
        url: "/images/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Innovatio Pro - Digital Solutions",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Innovatio Pro - Digital Solutions & Web Development",
    description:
      "Professional web development, mobile apps, and digital solutions for modern businesses.",
    images: ["/images/og-image.jpg"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
  },
};

// Client-side components with error boundaries
function ClientSideProviders({ children }: { children: React.ReactNode }) {
  return (
    <ClientOnly fallback={<div style={{ minHeight: "100vh" }}>{children}</div>}>
      {children}
    </ClientOnly>
  );
}

// Client-side accessibility components to avoid SSR issues
function AccessibilityComponents() {
  if (typeof window === "undefined") return null;

  const {
    AccessibilityProvider,
  } = require("@/providers/AccessibilityProvider");
  const { AccessibilityMenu } = require("@/components/ui/AccessibilityMenu");
  const { CookieConsent } = require("@/components/ui/CookieConsent");
  const GoogleAnalytics = require("@/components/GoogleAnalytics").default;
  const WebVitalsOptimizer =
    require("@/components/performance/WebVitalsOptimizer").default;

  return (
    <AccessibilityProvider>
      <AccessibilityMenu />
      <CookieConsent />
      <GoogleAnalytics />
      <WebVitalsOptimizer />
    </AccessibilityProvider>
  );
}

// The lang attribute will be set by the locale layout based on URL path
export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {/* Preconnect to external domains for performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />
        <link rel="preconnect" href="https://www.google-analytics.com" />
        <link rel="preconnect" href="https://www.googletagmanager.com" />

        {/* DNS prefetch for common external resources */}
        <link rel="dns-prefetch" href="//images.unsplash.com" />
        <link rel="dns-prefetch" href="//api.github.com" />

        {/* Favicon and app icons */}
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        {/* <link rel="manifest" href="/manifest.json" /> */}

        {/* Performance optimization */}
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, viewport-fit=cover"
        />
        <meta httpEquiv="X-UA-Compatible" content="IE=edge" />

        {/* Security headers */}
        <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
        <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />

        {/* Performance hints */}
        {/* <link rel="preload" as="style" href="/styles/critical.css" /> */}
      </head>
      <body
        className={`${inter.className} antialiased`}
        suppressHydrationWarning
      >
        {/* Skip Link für bessere Barrierefreiheit - direkt zum Hauptinhalt springen */}
        <a
          href="#main-content"
          className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-white dark:bg-gray-800 text-primary dark:text-[#8fa3c7] px-4 py-2 rounded-md shadow-md focus:outline-none"
          tabIndex={0}
        >
          Zum Hauptinhalt springen
        </a>
        <ThemeProvider>
          {children}

          {/* Client-side only components */}
          <ClientSideProviders>
            <AccessibilityComponents />
          </ClientSideProviders>

          {/* Analytics */}
          <SpeedInsights />
          <Analytics />
        </ThemeProvider>
      </body>
    </html>
  );
}
