// Critical components (loaded immediately)
import { HeroSection } from "@/components/sections/HeroSection";
import { getDictionary } from "@/lib/dictionaries";
import ClientOnly from "@/components/ui/ClientOnly";

// Direct imports for all components temporarily to isolate webpack issue
import { TrustedBySection } from "@/components/sections/TrustedBySection";
import { SolutionsPortfolioSection } from "@/components/sections/SolutionsPortfolioSection";
import { ServiceSection } from "@/components/sections/ServiceSection";
import { AboutSection } from "@/components/sections/AboutSection";
import { FooterSection } from "@/components/sections/FooterSection";
import { TestimonialsSection } from "@/components/sections/TestimonialsSection";
import { ContactSection } from "@/components/sections/ContactSection";
import PricingSectionWrapper from "@/components/pricing/PricingSectionWrapper";

// Dynamic imports only for potentially problematic components
import {
  DynamicFloatingContact,
  DynamicAIContentOptimizer,
  DynamicFloatingBlog,
} from "@/components/DynamicComponents";

interface HomeProps {
  params: Promise<{ locale: string }>;
}

export default async function Home({ params }: HomeProps) {
  const { locale } = await params;
  // Get the dictionary for the current locale
  const dict = await getDictionary(locale);

  // Use the actual products data
  const products = [
    {
      title: "TOGG App",
      link: "#",
      thumbnail: "/images/mockups/app_togg.png",
    },
    {
      title: "Spotz App 3",
      link: "#",
      thumbnail: "/images/mockups/app_spotz3.png",
    },
    {
      title: "Spotz App 2",
      link: "#",
      thumbnail: "/images/mockups/app_spotz2.png",
    },
    {
      title: "Spotz App",
      link: "#",
      thumbnail: "/images/mockups/app_spotz.png",
    },
    {
      title: "Spotz App 1",
      link: "#",
      thumbnail: "/images/mockups/app_spotz_1.png",
    },
    {
      title: "Room App",
      link: "#",
      thumbnail: "/images/mockups/app_room.png",
    },
    {
      title: "Plan App",
      link: "#",
      thumbnail: "/images/mockups/app_plan.png",
    },
    {
      title: "Medical App",
      link: "#",
      thumbnail: "/images/mockups/app_medical.png",
    },
    {
      title: "HostIQ App",
      link: "#",
      thumbnail: "/images/mockups/app_hostiq_1.png",
    },
    {
      title: "Food App",
      link: "#",
      thumbnail: "/images/mockups/app_food.png",
    },
    {
      title: "TOGG App 2",
      link: "#",
      thumbnail: "/images/mockups/app_togg.png",
    },
    {
      title: "Spotz App 4",
      link: "#",
      thumbnail: "/images/mockups/app_spotz3.png",
    },
    {
      title: "Room App 2",
      link: "#",
      thumbnail: "/images/mockups/app_room.png",
    },
    {
      title: "Plan App 2",
      link: "#",
      thumbnail: "/images/mockups/app_plan.png",
    },
    {
      title: "Medical App 2",
      link: "#",
      thumbnail: "/images/mockups/app_medical.png",
    },
  ];

  return (
    <main
      className={`flex min-h-screen flex-col items-center w-full overflow-x-hidden justify-between ${
        locale === "ar" ? "rtl" : ""
      }`}
    >
      {/* Critical above-the-fold content */}
      <HeroSection dictionary={dict.hero} />

      {/* Load all components directly to test webpack issue */}
      <TrustedBySection dictionary={dict.hero.trustedBy} />
      <SolutionsPortfolioSection
        dictionary={dict.solutionsPortfolio}
        products={products}
      />
      <ServiceSection dictionary={dict.serviceSection} />
      <AboutSection dictionary={dict.about} clientsDictionary={dict.clients} />
      <PricingSectionWrapper dictionary={dict.prices} locale={locale} />
      <TestimonialsSection dictionary={dict.testimonials} />
      <ContactSection dictionary={dict.contact} />

      {/* Only these remain dynamic */}
      <ClientOnly>
        <DynamicFloatingContact dictionary={dict.contact} />
        <DynamicFloatingBlog locale={locale} />
        <DynamicAIContentOptimizer
          locale={locale}
          pageType="home"
          primaryKeywords={[
            "Web Development",
            "Mobile App Development",
            "Digital Solutions",
            "Next.js Development",
            "Flutter Development",
          ]}
          secondaryKeywords={[
            "AI Integration",
            "Digital Transformation",
            "UX/UI Design",
            "Custom Software",
            "Business Solutions",
          ]}
          contentCategory="Digital Solutions"
          entityType="Organization"
        />
      </ClientOnly>

      {/* Footer */}
      <FooterSection dictionary={dict.footer} locale={locale} />
    </main>
  );
}
