'use client'

import { useState, useMemo, useCallback, memo, useEffect } from "react";
import { motion, AnimatePresence, useReducedMotion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import dynamic from "next/dynamic";
import { Section } from "@/components/ui/Section";
import { Button } from "@/components/ui/Button";
import { useI18n } from "@/providers/I18nProvider";
import { type Dictionary } from "@/lib/dictionary";
import {
  SERVICES,
  BUDGET_RANGES,
  TIMELINE_OPTIONS,
  SOURCE_OPTIONS,
  formatPrice,
  getServicePrice,
  getServiceById,
} from "@/lib/config/services";
import { EnhancedContactFormData } from "@/types/proposal";
import {
  Mail,
  Phone,
  Calendar,
  MapPin,
  Send,
  CheckCircle,
  XCircle,
  Clock,
  Shield,
  Zap,
  Star,
  Award,
  Verified,
  TrendingUp,
  Users,
  Globe,
  Sparkles,
  MessageCircle,
  Headphones,
  ArrowRight,
  Calculator,
  Building,
  DollarSign,
  Package,
  ChevronDown,
} from "lucide-react";

// Contact details from Flutter code - memoized
const contactInfo = {
  email: "<EMAIL>",
  phone: "+49 175 9918357",
  location: "Wyoming",
  calendlyLink: "https://calendly.com/v-hermann-it",
};

interface ContactSectionProps {
  dictionary: Dictionary["contact"] & {
    location?: string;
  };
  aboutDictionary?: Dictionary["about"];
}

// Memoized contact method card component
const ContactMethodCard = memo(
  ({
    method,
    index,
    hoveredContact,
    setHoveredContact,
  }: {
    method: any;
    index: number;
    hoveredContact: string | null;
    setHoveredContact: (id: string | null) => void;
  }) => {
    const handleMouseEnter = useCallback(() => {
      setHoveredContact(method.id);
    }, [method.id, setHoveredContact]);

    const handleMouseLeave = useCallback(() => {
      setHoveredContact(null);
    }, [setHoveredContact]);

    return (
      <motion.a
        key={method.id}
        href={method.action}
        target={
          method.id === "calendar" || method.id === "website"
            ? "_blank"
            : method.id === "location"
              ? "_self"
              : undefined
        }
        rel={
          method.id === "calendar" || method.id === "website"
            ? "noopener noreferrer"
            : undefined
        }
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        className="group block"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: index * 0.1 }}
      >
        <div className="relative p-6 rounded-2xl transition-all duration-300 hover:shadow-xl bg-white/60 dark:bg-gray-800/60 backdrop-blur-xl border border-white/30 dark:border-gray-700/30 hover:border-blue-300/50 dark:hover:border-blue-500/30 h-full">
          <div className="absolute top-4 right-4">
            <div
              className={`px-3 py-1 rounded-full text-xs font-semibold shadow-md bg-gradient-to-r ${method.gradient} text-white border border-white/20`}
            >
              {method.trustBadge}
            </div>
          </div>

          <div className="flex flex-col items-start gap-4 relative z-10 h-full">
            <div
              className={`p-4 rounded-xl transition-all duration-300 bg-gradient-to-r ${method.gradient} shadow-lg`}
            >
              <div className="text-white">{method.icon}</div>
            </div>
            <div className="flex-1 w-full">
              <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                {method.title}
              </h4>
              <p className="text-gray-700 dark:text-gray-300 font-medium mb-2">
                {method.value}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-1">
                {method.description}
              </p>
              <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                <Clock className="w-3 h-3 mr-1" />
                <span>{method.availability}</span>
              </div>
            </div>
            <div className="text-gray-400 dark:text-gray-500 group-hover:text-blue-500 dark:group-hover:text-blue-400 transition-all self-end">
              <ArrowRight className="w-5 h-5" />
            </div>
          </div>
        </div>
      </motion.a>
    );
  }
);

ContactMethodCard.displayName = "ContactMethodCard";

// Memoized WhatsApp quick action component
const WhatsAppQuickAction = memo(
  ({
    href,
    children,
    icon: Icon,
  }: {
    href: string;
    children: React.ReactNode;
    icon: any;
  }) => (
    <a
      href={href}
      target="_blank"
      rel="noopener noreferrer"
      className="flex items-center justify-between p-4 bg-white/60 dark:bg-gray-800/60 rounded-xl border border-green-200/50 dark:border-green-800/30 hover:shadow-md transition-all duration-300 group"
    >
      <div className="flex items-center gap-3">
        <div className="w-10 h-10 rounded-full bg-gradient-to-r from-green-500 to-emerald-600 flex items-center justify-center">
          <Icon className="w-5 h-5 text-white" />
        </div>
        <div>{children}</div>
      </div>
      <ArrowRight className="w-5 h-5 text-green-600 dark:text-green-400 group-hover:translate-x-1 transition-transform" />
    </a>
  )
);

WhatsAppQuickAction.displayName = "WhatsAppQuickAction";

// Dynamic import for client-side only form to prevent hydration issues
const ContactForm = dynamic(
  () => import("./ContactForm").then((mod) => ({ default: mod.ContactForm })),
  {
    ssr: false,
    loading: () => (
      <div className="relative bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl p-8 rounded-3xl shadow-2xl border border-white/30 dark:border-gray-700/30">
        <div className="animate-pulse space-y-6">
          <div className="h-12 bg-gray-200 dark:bg-gray-700 rounded-xl"></div>
          <div className="h-12 bg-gray-200 dark:bg-gray-700 rounded-xl"></div>
          <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded-xl"></div>
          <div className="h-12 bg-gray-200 dark:bg-gray-700 rounded-xl"></div>
        </div>
      </div>
    ),
  }
);

export const ContactSection = ({
  dictionary,
  aboutDictionary,
}: ContactSectionProps) => {
  const [hoveredContact, setHoveredContact] = useState<string | null>(null);

  // Contact methods data
  const contactMethods = useMemo(
    () => [
      {
        id: "email",
        icon: <Mail className="w-6 h-6" />,
        title: dictionary?.email || "Email",
        value: contactInfo.email,
        description: "Send a message anytime",
        availability: "Response within 24h",
        gradient: "from-blue-500 to-cyan-600",
        trustBadge: "📧 Professional",
        action: `mailto:${contactInfo.email}`,
      },
      {
        id: "phone",
        icon: <Phone className="w-6 h-6" />,
        title: dictionary?.phone || "Phone",
        value: contactInfo.phone,
        description: "Direct line for urgent matters",
        availability: "Mon-Fri 9-18 CET",
        gradient: "from-green-500 to-emerald-600",
        trustBadge: "☎️ Direct",
        action: `tel:${contactInfo.phone}`,
      },
      {
        id: "calendar",
        icon: <Calendar className="w-6 h-6" />,
        title: "Schedule Meeting",
        value: "Book a call",
        description: "15-30 min consultation",
        availability: "Multiple time slots",
        gradient: "from-purple-500 to-indigo-600",
        trustBadge: "📅 Free",
        action: contactInfo.calendlyLink,
      },
      {
        id: "location",
        icon: <MapPin className="w-6 h-6" />,
        title: dictionary?.location || "Location",
        value: contactInfo.location,
        description: "Remote work worldwide",
        availability: "Available globally",
        gradient: "from-orange-500 to-red-500",
        trustBadge: "🌍 Global",
        action: "#contact",
      },
    ],
    [dictionary]
  );

  return (
    <Section
      id="contact"
      title={dictionary?.title || "Contact"}
      subtitle={dictionary?.subtitle || "Let's start your project"}
      titleClassName="text-gray-900 dark:text-white"
      subtitleClassName="text-gray-600 dark:text-gray-300"
    >
      <div className="container mx-auto px-6 relative">
        <div className="absolute top-0 right-0 w-72 h-72 bg-blue-400/10 rounded-full blur-3xl opacity-60"></div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 lg:gap-16">
          {/* Enhanced Contact Methods */}
          <motion.div
            className="relative order-2 md:order-1"
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h3 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center md:text-left">
              {dictionary?.contactInfo || "Get In Touch"}
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {contactMethods.map((method, index) => (
                <ContactMethodCard
                  key={method.id}
                  method={method}
                  index={index}
                  hoveredContact={hoveredContact}
                  setHoveredContact={setHoveredContact}
                />
              ))}
            </div>

            {/* WhatsApp Quick Contact - Full Width */}
            <motion.div
              className="mt-8 p-6 rounded-2xl bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200/50 dark:border-green-800/30 relative"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.9, duration: 0.6 }}
            >
              <div className="absolute top-4 right-4">
                <div className="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center">
                  <MessageCircle className="w-4 h-4 text-white" />
                </div>
              </div>

              <h4 className="font-bold text-gray-800 dark:text-gray-200 mb-3 text-lg">
                Quick WhatsApp Contact
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Get instant replies and quick project discussions via WhatsApp.
              </p>

              <div className="grid grid-cols-1 gap-3">
                <WhatsAppQuickAction
                  href="https://wa.me/491759918357?text=Hi! I'm interested in discussing a new project with you. Could you please share more details about your services and availability?"
                  icon={MessageCircle}
                >
                  <div className="font-semibold text-gray-800 dark:text-gray-200">
                    New Project Inquiry
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Discuss your project requirements
                  </div>
                </WhatsAppQuickAction>

                <WhatsAppQuickAction
                  href="https://wa.me/491759918357?text=Hello! I'd like to get a quote for my project. Could you please send me information about your pricing and process?"
                  icon={Calculator}
                >
                  <div className="font-semibold text-gray-800 dark:text-gray-200">
                    Get Quote
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Request pricing information
                  </div>
                </WhatsAppQuickAction>

                <WhatsAppQuickAction
                  href="https://wa.me/491759918357?text=Hi! I have some technical questions about development. Could we have a quick chat about my requirements?"
                  icon={Headphones}
                >
                  <div className="font-semibold text-gray-800 dark:text-gray-200">
                    Technical Questions
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Quick technical consultation
                  </div>
                </WhatsAppQuickAction>
              </div>
            </motion.div>
          </motion.div>

          {/* Enhanced Contact Form */}
          <motion.div
            className="order-1 md:order-2"
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            {/* Client-side only form to prevent hydration issues with browser extensions */}
            <ContactForm dictionary={dictionary} />
          </motion.div>
        </div>
      </div>
    </Section>
  );
};