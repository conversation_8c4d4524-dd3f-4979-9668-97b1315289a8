import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { match as matchLocale } from '@formatjs/intl-localematcher';
import Negotiator from 'negotiator';

// List of all locales
export const locales = ['en', 'de', 'ru', 'tr', 'ar'];
export const defaultLocale = 'en';

// Country to language mapping
const countryLanguageMap: Record<string, string> = {
    // German-speaking countries
    DE: "de", // Germany
    AT: "de", // Austria
    CH: "de", // Switzerland

    // Russian-speaking countries
    RU: "ru", // Russia
    BY: "ru", // Belarus
    KZ: "ru", // Kazakhstan

    // Turkish-speaking countries
    TR: "tr", // Turkey
    CY: "tr", // Cyprus (Northern)

    // Arabic-speaking countries
    AE: "ar", // UAE - Dubai
    SA: "ar", // Saudi Arabia
    EG: "ar", // Egypt
    JO: "ar", // Jordan
    QA: "ar", // Qatar
    BH: "ar", // Bahrain
    KW: "ar", // Kuwait
    OM: "ar", // Oman
    IQ: "ar", // Iraq

    // All others default to English
};

// Get user's country from request headers
function getUserCountry(request: NextRequest): string | null {
    // Check for testing override using URL parameters
    const url = new URL(request.url);
    const countryParam = url.searchParams.get("country");
    if (countryParam) {
        return countryParam.toUpperCase();
    }

    // Get hostname for domain-based detection
    const host = request.headers.get("host") || "";

    // PRODUCTION URL DETECTION - Force German for innovatio-pro.com
    if (host.includes("innovatio-pro.com")) {
        return "DE";
    }

    // Try to get country from Cloudflare or Vercel headers
    const cfCountry = request.headers.get("cf-ipcountry");
    if (cfCountry) {
        return cfCountry;
    }

    const xVercelIpCountry = request.headers.get("x-vercel-ip-country");
    if (xVercelIpCountry) {
        return xVercelIpCountry;
    }

    // For local development, force German
    if (host.includes("localhost") || host.includes("127.0.0.1")) {
        return "DE";
    }

    return null;
}

// Get the preferred locale from the request
function getLocale(request: NextRequest): string {
    const negotiatorHeaders: Record<string, string> = {};
    request.headers.forEach((value, key) => (negotiatorHeaders[key] = value));

    // Check if locale is explicitly set in URL
    const url = new URL(request.url);
    const localeParam = url.searchParams.get("locale");
    if (localeParam && locales.includes(localeParam as any)) {
        return localeParam as any;
    }

    // First check if we can determine locale from country
    const country = getUserCountry(request);
    if (country) {
        const countryLocale = countryLanguageMap[country];
        if (countryLocale && locales.includes(countryLocale)) {
            return countryLocale;
        }
    }

    // Use negotiator and intl-localematcher to get best locale from browser settings
    let languages = new Negotiator({ headers: negotiatorHeaders }).languages();
    const detectedLocale = matchLocale(languages, locales, defaultLocale);
    return detectedLocale;
}

export function middleware(request: NextRequest) {
    const url = new URL(request.url);
    const pathname = url.pathname;
    const host = request.headers.get("host") || "";

    // Skip middleware for certain paths
    if (
        pathname.startsWith('/_next') ||
        pathname.startsWith('/api') ||
        pathname.includes('.') ||
        pathname.startsWith('/favicon')
    ) {
        return NextResponse.next();
    }

    // Check if pathname already has a locale
    const pathnameHasLocale = locales.some(
        (locale) => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
    );

    if (!pathnameHasLocale) {
        // Get the locale from user preferences
        const locale = getLocale(request);

        // Redirect to locale-prefixed path
        const newUrl = new URL(`/${locale}${pathname}`, request.url);
        return NextResponse.redirect(newUrl);
    }

    return NextResponse.next();
}

export const config = {
    matcher: [
        // Skip all internal paths (_next)
        '/((?!_next|api|favicon.ico).*)',
        // Optional: only run on root (/) URL
        // '/'
    ],
}; 