"use client";

import { useState, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { type Dictionary } from "@/lib/dictionary";
import { EnhancedContactFormData } from "@/types/proposal";
import {
  Send,
  CheckCircle,
  XCircle,
  Shield,
  ChevronDown,
  Package,
  DollarSign,
  Calendar,
  Globe,
} from "lucide-react";
import {
  getAllServices,
  BUDGET_RANGES,
  TIMELINE_OPTIONS,
  SOURCE_OPTIONS,
} from "@/lib/config/services";

interface ContactFormProps {
  dictionary: Dictionary["contact"] & {
    location?: string;
  };
}

export const ContactForm = ({ dictionary }: ContactFormProps) => {
  const [formData, setFormData] = useState<EnhancedContactFormData>({
    name: "",
    email: "",
    companyName: "",
    phone: "",
    message: "",
    selectedService: "",
    estimatedBudget: "",
    projectTimeline: "",
    heardAbout: "",
  });

  const [formStatus, setFormStatus] = useState<
    "idle" | "submitting" | "success" | "error"
  >("idle");

  const [responseData, setResponseData] = useState<{
    leadId?: string;
    proposalUrl?: string;
  } | null>(null);

  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();
      setFormStatus("submitting");

      try {
        const response = await fetch("/api/contact/enhanced", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(formData),
        });

        const result = await response.json();

        if (response.ok) {
          setFormStatus("success");
          setResponseData(result.data);
          // Reset form
          setFormData({
            name: "",
            email: "",
            companyName: "",
            phone: "",
            message: "",
            selectedService: "",
            estimatedBudget: "",
            projectTimeline: "",
            heardAbout: "",
          });
          setTimeout(() => setFormStatus("idle"), 8000);
        } else {
          throw new Error(result.error || "Failed to send message");
        }
      } catch (error) {
        console.error("Contact form error:", error);
        setFormStatus("error");
        setTimeout(() => setFormStatus("idle"), 5000);
      }
    },
    [formData]
  );

  const updateFormData = (
    field: keyof EnhancedContactFormData,
    value: string
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  if (formStatus === "success") {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="relative bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl p-8 rounded-3xl shadow-2xl border border-white/30 dark:border-gray-700/30"
      >
        <div className="text-center">
          <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Thank you for your inquiry!
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            We've received your message and will get back to you soon.
          </p>
          {responseData?.proposalUrl && (
            <div className="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg mb-4">
              <p className="text-sm text-blue-700 dark:text-blue-300 mb-2">
                A custom proposal has been prepared for you:
              </p>
              <a
                href={responseData.proposalUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 text-blue-600 dark:text-blue-400 hover:underline"
              >
                View Your Proposal
                <Shield className="w-4 h-4" />
              </a>
            </div>
          )}
          {responseData?.leadId && (
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Reference ID: {responseData.leadId}
            </p>
          )}
        </div>
      </motion.div>
    );
  }

  return (
    <form
      onSubmit={handleSubmit}
      className="relative bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl p-8 rounded-3xl shadow-2xl border border-white/30 dark:border-gray-700/30"
      suppressHydrationWarning
    >
      <div className="space-y-6">
        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Name *
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={(e) => updateFormData("name", e.target.value)}
              placeholder="Your Name"
              required
              className="w-full px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-gray-600 bg-white/60 dark:bg-gray-700/60 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:outline-none transition-colors"
              data-1p-ignore
              data-lpignore="true"
              autoComplete="off"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Email *
            </label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={(e) => updateFormData("email", e.target.value)}
              placeholder="<EMAIL>"
              required
              className="w-full px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-gray-600 bg-white/60 dark:bg-gray-700/60 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:outline-none transition-colors"
              data-1p-ignore
              data-lpignore="true"
              autoComplete="off"
            />
          </div>
        </div>

        {/* Service Selection */}
        <div>
          <label className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            <Package className="w-4 h-4" />
            Interested Service
          </label>
          <div className="relative">
            <select
              name="selectedService"
              value={formData.selectedService}
              onChange={(e) =>
                updateFormData("selectedService", e.target.value)
              }
              className="w-full px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-gray-600 bg-white/60 dark:bg-gray-700/60 text-gray-900 dark:text-white focus:border-blue-500 focus:outline-none transition-colors appearance-none"
            >
              <option value="">Select a service...</option>
              {getAllServices().map((service) => (
                <option key={service.id} value={service.id}>
                  {service.name}
                </option>
              ))}
            </select>
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
          </div>
        </div>

        {/* Company and Phone */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Company (Optional)
            </label>
            <input
              type="text"
              name="companyName"
              value={formData.companyName}
              onChange={(e) => updateFormData("companyName", e.target.value)}
              placeholder="Your Company Name"
              className="w-full px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-gray-600 bg-white/60 dark:bg-gray-700/60 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:outline-none transition-colors"
              data-1p-ignore
              data-lpignore="true"
              autoComplete="off"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Phone (Optional)
            </label>
            <input
              type="tel"
              name="phone"
              value={formData.phone}
              onChange={(e) => updateFormData("phone", e.target.value)}
              placeholder="+49 ************"
              className="w-full px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-gray-600 bg-white/60 dark:bg-gray-700/60 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:outline-none transition-colors"
              data-1p-ignore
              data-lpignore="true"
              autoComplete="off"
            />
          </div>
        </div>

        {/* Budget and Timeline */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <DollarSign className="w-4 h-4" />
              Estimated Budget
            </label>
            <div className="relative">
              <select
                name="estimatedBudget"
                value={formData.estimatedBudget}
                onChange={(e) =>
                  updateFormData("estimatedBudget", e.target.value)
                }
                className="w-full px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-gray-600 bg-white/60 dark:bg-gray-700/60 text-gray-900 dark:text-white focus:border-blue-500 focus:outline-none transition-colors appearance-none"
              >
                <option value="">Select budget range...</option>
                {BUDGET_RANGES.map((range) => (
                  <option key={range.value} value={range.value}>
                    {range.label}
                  </option>
                ))}
              </select>
              <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
            </div>
          </div>
          <div>
            <label className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <Calendar className="w-4 h-4" />
              Project Timeline
            </label>
            <div className="relative">
              <select
                name="projectTimeline"
                value={formData.projectTimeline}
                onChange={(e) =>
                  updateFormData("projectTimeline", e.target.value)
                }
                className="w-full px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-gray-600 bg-white/60 dark:bg-gray-700/60 text-gray-900 dark:text-white focus:border-blue-500 focus:outline-none transition-colors appearance-none"
              >
                <option value="">Select timeline...</option>
                {TIMELINE_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
            </div>
          </div>
        </div>

        {/* How did you hear about us */}
        <div>
          <label className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            <Globe className="w-4 h-4" />
            How did you hear about us?
          </label>
          <div className="relative">
            <select
              name="heardAbout"
              value={formData.heardAbout}
              onChange={(e) => updateFormData("heardAbout", e.target.value)}
              className="w-full px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-gray-600 bg-white/60 dark:bg-gray-700/60 text-gray-900 dark:text-white focus:border-blue-500 focus:outline-none transition-colors appearance-none"
            >
              <option value="">Select source...</option>
              {SOURCE_OPTIONS.map((source) => (
                <option key={source.value} value={source.value}>
                  {source.label}
                </option>
              ))}
            </select>
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
          </div>
        </div>

        {/* Project Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Project Description *
          </label>
          <textarea
            name="message"
            value={formData.message}
            onChange={(e) => updateFormData("message", e.target.value)}
            placeholder="Tell me about your project, goals, requirements, and any specific features you have in mind..."
            required
            rows={5}
            className="w-full px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-gray-600 bg-white/60 dark:bg-gray-700/60 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:outline-none transition-colors resize-none"
            data-1p-ignore
            data-lpignore="true"
          />
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            The more details you provide, the better I can understand your needs
            and provide an accurate proposal.
          </p>
        </div>

        {/* Submit Button */}
        <motion.button
          type="submit"
          disabled={formStatus === "submitting"}
          className="w-full py-4 px-6 rounded-xl bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-2"
          whileHover={{ scale: formStatus !== "submitting" ? 1.02 : 1 }}
          whileTap={{ scale: formStatus !== "submitting" ? 0.98 : 1 }}
        >
          {formStatus === "submitting" ? (
            <>
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
              Sending...
            </>
          ) : (
            <>
              <Send className="w-5 h-5" />
              Send Message
            </>
          )}
        </motion.button>

        {/* Error State */}
        <AnimatePresence>
          {formStatus === "error" && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="flex items-center gap-3 p-4 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-xl"
            >
              <XCircle className="w-5 h-5 text-red-500 flex-shrink-0" />
              <p className="text-red-700 dark:text-red-300 text-sm">
                Failed to send message. Please try again or contact us directly.
              </p>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </form>
  );
};
