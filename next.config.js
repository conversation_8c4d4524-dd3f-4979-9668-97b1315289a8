/** @type {import('next').NextConfig} */
// Note: Next.js 15.3.3 has known issues with experimental features
// optimizePackageImports and optimizeCss causing webpack module resolution errors
// "Cannot read properties of undefined (reading 'call')"
// These features are disabled until the issue is resolved in a future Next.js version
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: false,
    formats: ["image/avif", "image/webp"],
    minimumCacheTTL: 60,
    remotePatterns: [
      {
        protocol: "https",
        hostname: "images.unsplash.com",
      },
    ],
  },
  experimental: {
    scrollRestoration: true,
    // Temporarily disable these features due to Next.js 15.3.3 webpack issues
    // optimizePackageImports: [
    //   'lucide-react',
    //   '@heroicons/react',
    //   '@tabler/icons-react',
    //   'framer-motion',
    //   '@radix-ui/react-dialog',
    //   '@radix-ui/react-dropdown-menu',
    //   '@radix-ui/react-select',
    //   '@radix-ui/react-slider',
    //   '@radix-ui/react-tabs',
    //   'react-intersection-observer'
    // ],
    // optimizeCss: false, // Disabled due to webpack module resolution errors
  },
  serverExternalPackages: ["sharp"],
  compiler: {
    removeConsole:
      process.env.NODE_ENV === "production"
        ? {
            exclude: ["error"],
          }
        : false,
  },
  compress: true,
  poweredByHeader: false,
  webpack: (config, { dev, isServer }) => {
    // SVG optimization
    config.module.rules.push({
      test: /\.svg$/,
      use: ["@svgr/webpack"],
    });

    // Ensure proper module resolution for React
    // Remove any problematic alias configurations that might cause webpack errors
    if (config.resolve.alias) {
      // Remove React alias if it exists (can cause "Cannot read properties of undefined" errors)
      delete config.resolve.alias["react"];
      delete config.resolve.alias["react-dom"];
    }

    return config;
  },
  // Headers for security and performance
  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          {
            key: "X-Frame-Options",
            value: "DENY",
          },
          {
            key: "X-XSS-Protection",
            value: "1; mode=block",
          },
          {
            key: "Referrer-Policy",
            value: "origin-when-cross-origin",
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;