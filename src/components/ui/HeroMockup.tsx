"use client";

import React from "react";
import { motion, useReducedMotion } from "framer-motion";
import { <PERSON><PERSON><PERSON>, Play, Heart, MessageCircle, Share } from "lucide-react";
import Image from "next/image";

interface HeroMockupProps {
  imageSrc?: string;
  imageAlt?: string;
  className?: string;
  appScreenshots?: string[];
}

const HeroMockup: React.FC<HeroMockupProps> = ({
  imageSrc,
  imageAlt = "App mockup",
  className = "",
  appScreenshots = [],
}) => {
  const shouldReduceMotion = useReducedMotion();
  const [currentImageIndex, setCurrentImageIndex] = React.useState(0);

  // Auto-cycle through images if not reducing motion
  React.useEffect(() => {
    if (!shouldReduceMotion && appScreenshots.length > 1) {
      const interval = setInterval(() => {
        setCurrentImageIndex((prev) => (prev + 1) % appScreenshots.length);
      }, 4000); // Change image every 4 seconds
      return () => clearInterval(interval);
    }
  }, [shouldReduceMotion, appScreenshots.length]);

  return (
    <div className={`relative w-full h-full flex items-center justify-center ${className}`}>
      {/* Main mockup container - Komplett schwarzer Rahmen */}
      <motion.div
        className="relative w-56 sm:w-72 lg:w-80 h-[480px] sm:h-[580px] lg:h-[640px] bg-black rounded-[2.5rem] sm:rounded-[3rem] p-2 sm:p-3 shadow-2xl shadow-black/50 border border-black"
        initial={{ opacity: 0, x: 100, rotateY: 15 }}
        animate={{ 
          opacity: 1, 
          x: 0, 
          rotateY: 0,
          rotateZ: shouldReduceMotion ? 0 : [0, 1 , -1, 0]
        }}
        transition={{ 
          duration: shouldReduceMotion ? 0.3 : 1.2, 
          delay: shouldReduceMotion ? 0 : 0.3, 
          ease: "easeOut",
          rotateZ: {
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }
        }}
        whileHover={shouldReduceMotion ? {} : { 
          scale: 1.02,
          rotateY: -2,
          boxShadow: '0 25px 50px -12px rgba(255, 255, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2)',
          transition: { duration: 0.3 }
        }}
      >
        {/* Glanz-Overlay nur beim Hover */}
        <motion.div
          className="absolute inset-0 rounded-[2.5rem] sm:rounded-[3rem] pointer-events-none opacity-0"
          style={{
            background: 'linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.15) 50%, transparent 70%)',
            backgroundSize: '200% 200%',
            backgroundPosition: '-200% -200%'
          }}
          whileHover={shouldReduceMotion ? {} : {
            opacity: 1,
            backgroundPosition: '200% 200%',
            transition: { duration: 0.6, ease: "easeInOut" }
          }}
        />
        {/* Phone screen */}
        <div className="w-full h-full bg-black rounded-[2.5rem] sm:rounded-[3rem] overflow-hidden relative shadow-inner">
          {/* Dynamic notch - realistischer */}
          <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-20 sm:w-36 h-4 sm:h-7 bg-black rounded-b-2xl sm:rounded-b-3xl z-20 shadow-lg">
            <div className="absolute top-1 sm:top-2 left-1/2 transform -translate-x-1/2 w-8 sm:w-12 h-0.5 sm:h-1 bg-gray-800 rounded-full"></div>
            <div className="absolute top-1 sm:top-2 right-1.5 sm:right-3 w-1 sm:w-2 h-1 sm:h-2 bg-gray-800 rounded-full"></div>
          </div>
          
          {/* Status bar - iOS-Style */}
          <div className="h-8 sm:h-8 bg-gradient-to-b from-gray-50 to-white flex items-center justify-between px-6 sm:px-8 text-sm font-semibold text-black pt-0 relative z-10">
            <span className="font-bold">10:10</span>
            <div className="flex items-center gap-1">

              <div className="w-6 h-3 border border-black rounded-sm ml-1">
                <div className="w-4 h-2 bg-green-500 rounded-sm m-0.5"></div>
              </div>
            </div>
          </div>
          
          {/* App content */}
          {appScreenshots.length > 0 ? (
            <div className="relative w-full h-full bg-white overflow-hidden" style={{ height: 'calc(100% - 3rem)' }}>
              {appScreenshots.map((screenshot, index) => {
                const isActive = shouldReduceMotion ? index === 0 : index === currentImageIndex;
                return (
                  <motion.div
                    key={index}
                    className="absolute inset-0 w-full h-full"
                    initial={{ opacity: 0 }}
                    animate={{ 
                      opacity: isActive ? 1 : 0,
                      zIndex: isActive ? 10 : 0
                    }}
                    transition={{ 
                      duration: 0.8,
                      ease: "easeInOut"
                    }}
                  >
                    <Image
                      src={screenshot}
                      alt={`App Screenshot ${index + 1}`}
                      fill
                      className="object-cover object-top"
                      sizes="(max-width: 640px) 320px, (max-width: 1024px) 384px, 448px"
                      priority={index === 0}
                    />
                  </motion.div>
                );
              })}
            </div>
          ) : imageSrc ? (
            <div className="relative w-full flex-1 bg-white">
              <Image
                src={imageSrc}
                alt={imageAlt}
                fill
                className="object-cover"
                sizes="(max-width: 640px) 320px, (max-width: 1024px) 384px, 448px"
              />
            </div>
          ) : (
            <div className="flex-1 bg-gradient-to-b from-white to-gray-50 relative">
              {/* App Header */}
              <motion.div 
                className="bg-white px-6 py-4 border-b border-gray-100"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: shouldReduceMotion ? 0 : 0.6, duration: 0.6 }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-md">
                      <Sparkles className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <div className="w-16 h-2 bg-gray-800 rounded mb-1"></div>
                      <div className="w-12 h-1.5 bg-gray-400 rounded"></div>
                    </div>
                  </div>
                  <div className="w-6 h-6 bg-gray-200 rounded-full"></div>
                </div>
              </motion.div>
              
              {/* Main Content */}
              <div className="p-6 space-y-6">
                {/* Featured Card */}
                <motion.div 
                  className="bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 rounded-3xl p-6 text-white shadow-xl"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: shouldReduceMotion ? 0 : 0.8, duration: 0.6 }}
                >
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-20 h-3 bg-white/40 rounded"></div>
                    <Heart className="w-5 h-5 text-white/80" />
                  </div>
                  <div className="space-y-2">
                    <div className="w-32 h-2 bg-white/30 rounded"></div>
                    <div className="w-24 h-2 bg-white/20 rounded"></div>
                  </div>
                  <div className="mt-4 flex items-center space-x-2">
                    <Play className="w-4 h-4 text-white" />
                    <div className="w-16 h-2 bg-white/50 rounded"></div>
                  </div>
                </motion.div>
                
                {/* Content Grid */}
                <motion.div 
                  className="grid grid-cols-2 gap-4"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: shouldReduceMotion ? 0 : 1.0, duration: 0.6 }}
                >
                  <div className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
                    <div className="w-6 h-6 bg-green-500 rounded-lg mb-3"></div>
                    <div className="space-y-2">
                      <div className="w-16 h-2 bg-gray-300 rounded"></div>
                      <div className="w-12 h-1.5 bg-gray-200 rounded"></div>
                    </div>
                  </div>
                  <div className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
                    <div className="w-6 h-6 bg-orange-500 rounded-lg mb-3"></div>
                    <div className="space-y-2">
                      <div className="w-16 h-2 bg-gray-300 rounded"></div>
                      <div className="w-12 h-1.5 bg-gray-200 rounded"></div>
                    </div>
                  </div>
                </motion.div>
                
                {/* Social Feed */}
                <motion.div 
                  className="space-y-4"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: shouldReduceMotion ? 0 : 1.2, duration: 0.6 }}
                >
                  <div className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="w-8 h-8 bg-gradient-to-br from-pink-400 to-red-500 rounded-full"></div>
                      <div className="flex-1">
                        <div className="w-20 h-2 bg-gray-300 rounded mb-1"></div>
                        <div className="w-16 h-1.5 bg-gray-200 rounded"></div>
                      </div>
                    </div>
                    <div className="w-full h-20 bg-gradient-to-r from-blue-100 to-purple-100 rounded-xl mb-3"></div>
                    <div className="flex items-center justify-between">
                      <div className="flex space-x-4">
                        <Heart className="w-4 h-4 text-gray-400" />
                        <MessageCircle className="w-4 h-4 text-gray-400" />
                        <Share className="w-4 h-4 text-gray-400" />
                      </div>
                      <div className="w-8 h-1.5 bg-gray-200 rounded"></div>
                    </div>
                  </div>
                </motion.div>
              </div>
              
              {/* Bottom Navigation */}
              <motion.div 
                className="absolute bottom-0 left-0 right-0 bg-white/95 backdrop-blur-sm border-t border-gray-200 px-6 py-3"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: shouldReduceMotion ? 0 : 1.4, duration: 0.6 }}
              >
                <div className="flex items-center justify-around">
                  <div className="w-6 h-6 bg-blue-500 rounded-lg shadow-sm"></div>
                  <div className="w-6 h-6 bg-gray-300 rounded-lg"></div>
                  <div className="w-6 h-6 bg-gray-300 rounded-lg"></div>
                  <div className="w-6 h-6 bg-gray-300 rounded-lg"></div>
                </div>
              </motion.div>
            </div>
          )}
        </div>
        
        {/* Floating elements */}
        {!shouldReduceMotion && (
          <>
            <motion.div
              className="absolute -top-3 -right-3 sm:-top-4 sm:-right-4 w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full opacity-80 shadow-lg"
              animate={{
                y: [0, -8, 0],
                scale: [1, 1.1, 1],
                rotate: [0, 180, 360],
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
            <motion.div
              className="absolute -bottom-4 -left-4 sm:-bottom-6 sm:-left-6 w-4 h-4 sm:w-6 sm:h-6 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full opacity-60 shadow-lg"
              animate={{
                y: [0, 8, 0],
                x: [0, 4, 0],
                scale: [1, 0.8, 1],
              }}
              transition={{
                duration: 5,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 1.5,
              }}
            />
            <motion.div
              className="absolute top-1/3 -left-2 w-3 h-3 sm:w-4 sm:h-4 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full opacity-70"
              animate={{
                x: [0, -6, 0],
                y: [0, -4, 0],
                scale: [1, 1.2, 1],
              }}
              transition={{
                duration: 3.5,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 2,
              }}
            />
          </>
        )}
      </motion.div>
      
      {/* Ambient glow effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-blue-500/5 rounded-full blur-3xl -z-10 scale-150"></div>
    </div>
  );
};

HeroMockup.displayName = "HeroMockup";

export default HeroMockup;